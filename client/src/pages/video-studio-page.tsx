import React from "react";
import { useLocation } from "wouter";
import { FocusCards } from "@/components/ui/focus-cards";
import { 
  Video, 
  Wand2, 
  Camera, 
  Film, 
  Sparkles, 
  Zap, 
  Play, 
  Clapperboard 
} from "lucide-react";

export default function VideoStudioPage() {
  const [, navigate] = useLocation();

  const videoServices = [
    {
      title: "LumaLabs Dream Machine",
      src: "https://images.unsplash.com/photo-1536440136628-849c177e76a1?q=80&w=3000&auto=format&fit=crop&ixlib=rb-4.0.3",
      onClick: () => navigate("/video-studio/luma-labs")
    },
    {
      title: "Google Veo",
      src: "https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?q=80&w=3000&auto=format&fit=crop&ixlib=rb-4.0.3",
      onClick: () => navigate("/video-studio/google-veo")
    },
    {
      title: "Runway ML",
      src: "https://images.unsplash.com/photo-1485846234645-a62644f84728?q=80&w=3000&auto=format&fit=crop&ixlib=rb-4.0.3",
      onClick: () => navigate("/video-studio/runway-ml")
    },
    {
      title: "Texto a Video",
      src: "https://images.unsplash.com/photo-1516035069371-29a1b244cc32?q=80&w=3000&auto=format&fit=crop&ixlib=rb-4.0.3",
      onClick: () => navigate("/video-studio/text-to-video")
    },
    {
      title: "Imagen a Video",
      src: "https://images.unsplash.com/photo-1440404653325-ab127d49abc1?q=80&w=3000&auto=format&fit=crop&ixlib=rb-4.0.3",
      onClick: () => navigate("/image-to-video")
    },
    {
      title: "Editor de Video IA",
      src: "https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?q=80&w=3000&auto=format&fit=crop&ixlib=rb-4.0.3",
      onClick: () => navigate("/video-editor")
    },
    {
      title: "Generador de Shorts",
      src: "https://images.unsplash.com/photo-1611162617474-5b21e879e113?q=80&w=3000&auto=format&fit=crop&ixlib=rb-4.0.3",
      onClick: () => navigate("/video-studio/shorts-generator")
    },
    {
      title: "Video Automático",
      src: "https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?q=80&w=3000&auto=format&fit=crop&ixlib=rb-4.0.3",
      onClick: () => navigate("/video-generator")
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-12">
        {/* Header Section */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-6">
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-4 rounded-full">
              <Video className="w-12 h-12 text-white" />
            </div>
          </div>
          <h1 className="text-5xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-4">
            Video Studio
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Crea videos increíbles con inteligencia artificial. Desde texto hasta video, 
            edición profesional y generación automática de contenido.
          </p>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <div className="text-center p-6 bg-white/70 backdrop-blur-sm rounded-xl border border-white/20 shadow-lg">
            <div className="text-3xl font-bold text-purple-600 mb-2">8+</div>
            <div className="text-gray-600">Servicios de Video IA</div>
          </div>
          <div className="text-center p-6 bg-white/70 backdrop-blur-sm rounded-xl border border-white/20 shadow-lg">
            <div className="text-3xl font-bold text-blue-600 mb-2">HD</div>
            <div className="text-gray-600">Calidad Profesional</div>
          </div>
          <div className="text-center p-6 bg-white/70 backdrop-blur-sm rounded-xl border border-white/20 shadow-lg">
            <div className="text-3xl font-bold text-green-600 mb-2">24/7</div>
            <div className="text-gray-600">Disponibilidad</div>
          </div>
        </div>

        {/* Services Grid */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-gray-800 mb-8">
            Servicios Disponibles
          </h2>
          <FocusCards cards={videoServices} />
        </div>

        {/* Features Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-20">
          <div className="text-center p-6 bg-white/70 backdrop-blur-sm rounded-xl border border-white/20 shadow-lg">
            <div className="bg-purple-100 p-3 rounded-full w-fit mx-auto mb-4">
              <Wand2 className="w-8 h-8 text-purple-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">IA Avanzada</h3>
            <p className="text-gray-600 text-sm">
              Tecnología de última generación para crear videos profesionales
            </p>
          </div>

          <div className="text-center p-6 bg-white/70 backdrop-blur-sm rounded-xl border border-white/20 shadow-lg">
            <div className="bg-blue-100 p-3 rounded-full w-fit mx-auto mb-4">
              <Zap className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Rápido</h3>
            <p className="text-gray-600 text-sm">
              Genera videos en minutos, no en horas
            </p>
          </div>

          <div className="text-center p-6 bg-white/70 backdrop-blur-sm rounded-xl border border-white/20 shadow-lg">
            <div className="bg-green-100 p-3 rounded-full w-fit mx-auto mb-4">
              <Sparkles className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Calidad HD</h3>
            <p className="text-gray-600 text-sm">
              Videos en alta definición listos para publicar
            </p>
          </div>

          <div className="text-center p-6 bg-white/70 backdrop-blur-sm rounded-xl border border-white/20 shadow-lg">
            <div className="bg-orange-100 p-3 rounded-full w-fit mx-auto mb-4">
              <Play className="w-8 h-8 text-orange-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Fácil de Usar</h3>
            <p className="text-gray-600 text-sm">
              Interfaz intuitiva para todos los niveles
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
